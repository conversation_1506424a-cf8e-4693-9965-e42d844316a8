<?php
include_once("../../config/mainController.php"); // Incluye el Controlador Principal
include_once("./modelo.php");	// Incluye el Modelo.

$controller = new mainController(); // Instancia a la clase MainController
$modelo = new crear_preguntas(); // Instancia a la clase del modelo
try // Try, manejo de Errores
{
	$metodo = $_SERVER['REQUEST_METHOD'];
	$tipo_res = "";
	$response = null;
	$variables = array();

	// OBLIGATORIO: Manejar JSON y POST tradicional
	if (!empty($_POST)) {
		$variables = $_POST;
	} else if($metodo == 'POST' && $_SERVER['CONTENT_TYPE'] == 'application/json'){
		$json = file_get_contents('php://input');
		$variables = json_decode($json, true);
	}

	// OBLIGATORIO: Validar acción
	if (!isset($variables['accion'])) {
		http_response_code(404);
		header("Content-type: text/plain; charset=utf-8");
		echo "0";
		return;
	}

	$accion = $variables['accion'];
	// Dependiendo de la accion se ejecutaran las tareas y se definira el tipo de respuesta.
	switch ($accion) {
		case 'crud':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;

			$pregunta = $variables['pregunta'];
			$orden = $variables['orden'];
			$tipopc = $variables['tipopc'];
			$check_obliga = $variables['check_obliga'];
			$tipo_crud = $variables['tipo_crud'];
			$id_pre = $variables['id_pre'];
			$id_enc = $variables['id_enc'];
			$id_respuesta = $variables['id_respuesta'];
			if ($tipo_crud < 3) {
				$response = $modelo->recibirDatos($pregunta, $orden, $tipopc, $check_obliga, $tipo_crud, $id_pre, $id_enc, $id_respuesta);
			} else {

				$id_encuesta_origen = $variables['id_encuesta_origen'];
				$id_pre_origen = $variables['id_pre_origen'];
				$id_resp_origen = $variables['id_resp_origen'];

				$response = $modelo->duplicarPregunta($pregunta, $orden, $tipopc, $check_obliga, $tipo_crud, $id_pre, $id_enc, $id_respuesta, $id_encuesta_origen, $id_pre_origen, $id_resp_origen);
			}

			break;

		case 'ver_respuestas':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;

			$id_enc = $variables['id_enc'];
			$id_respuesta = $variables['id_respuesta'];

			$response = $modelo->ver_respuestas($id_enc, $id_respuesta);

			break;

		case 'info_encuesta':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;

			$id_enc = $variables['id_enc'];

			$response = $modelo->info_encuesta($id_enc);

			break;

		case 'ver_orden':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;

			$id_enc = $variables['id_enc'];
			$id_pregunta = $variables['id_pregunta'];
			$id_respuesta = $variables['id_respuesta'];

			$response = $modelo->ver_orden($id_enc, $id_pregunta, $id_respuesta);

			break;

		case 'Consultar':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;

			$tipop = $variables['tipop'];
			$obliga = $variables['obliga'];
			$respuestab = $variables['respuestab'];
			$estadob = $variables['estadob'];
			$id_enc = $variables['id_enc'];

			$datos = $modelo->Consultar($tipop, $obliga, $respuestab, $estadob, $id_enc);
			$response = array(
				"draw" => 1,
				"recordsTotal" => 0,
				"recordsFiltered" => 0,
				"data" => $datos
			);

			break;

		case 'deta_preguntas':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;

			$id_enc = $variables['id_enc'];

			$response = $modelo->Consultar_deta($id_enc);

			break;

		case 'Descargar_Csv':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;

			$titulob = $variables['titulob'];
			$tipob = $variables['tipob'];
			$estadob = $variables['estadob'];
			$userb = $variables['userb'];

			$response = $modelo->Consultar_csv($titulob, $tipob, $estadob, $userb);

			break;

		case 'cambiar_estado':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;

			$estado = $variables['estado'];
			$id_enc = $variables['id_enc'];
			$id_pre = $variables['id_pre'];

			$response = $modelo->cambiar_estado($estado, $id_enc, $id_pre);
			break;

		case 'eliminar':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;

			$id_enc = $variables['id_enc'];
			$id_pre = $variables['id_pre'];

			$response = $modelo->eliminar($id_enc, $id_pre);
			break;

		default:
			http_response_code(404);
			header("Content-type: text/plain; charset=utf-8");
			echo "0";
			return;
	}

	// OBLIGATORIO: Headers y manejo de respuestas
	if ($tipo_res == "JSON") {
		header("Content-type: application/json; charset=utf-8");
		$json_output = json_encode($response, true);
		if (json_last_error() !== JSON_ERROR_NONE) {
			throw new Exception("Error al codificar JSON: " . json_last_error_msg());
		} else {
			echo $json_output;
		}
	} elseif ($tipo_res == "HTML") {
		header("Content-type: text/html; charset=utf-8");
		echo $response;
	} else {
		header("Content-type: text/plain; charset=utf-8");
		echo $response;
	}
} // Fin Try
catch (Exception $e) {
	header("Content-type: application/json; charset=utf-8");
	http_response_code(500);
	echo json_encode(array("error" => true, "mensaje" => $e->getMessage()));
} catch (Error $e) {
	header("Content-type: application/json; charset=utf-8");
	http_response_code(500);
	echo json_encode(array("error" => true, "mensaje" => $e->getMessage()));
}
