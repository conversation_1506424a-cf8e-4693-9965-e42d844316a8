const URL_MODULO = 'modulos/crear_preguntas_v1.0.0';

var tipo_crud = 0;
var id_enc = localStorage.getItem('idEncuestaData');
var id_pre = 0;
var vigencia_enc = 0;
var cant_resp = 0;

var datos_reg = new Array();
var datos_dis = new Array();
var datos_ter = new Array();
var datos_zonas = new Array();

var in_regional = new Array();
var in_dis = new Array();
var in_ter = new Array();
var in_zonas = new Array();

var dtable;

$(document).ready(function () {

	var volver_enc = '<span style="cursor:pointer;font-size: 15px;" id="v_encuesta" ><i class="fa fa-file" aria-hidden="true" style="color: #10628a;"></i> <span style="color: #10628a;">Encuestas</span></span> <span style="font-weight: bold;" > > </span>';

	var volver_pre = ' <span style="font-size: 15px;" id="r_encuesta" ><i class="fa fa-file" aria-hidden="true" style="color:#8a8a8a;"></i> <span>Preguntas</span></span>';

	$(".box-header").append(volver_enc + volver_pre);

	$("#v_encuesta").click(function () {

		window.location = "?mod=crear_encuesta_v1.0.0";

	});

	//Funcion del boton Agregar..!
	$("#btn_crear").click(function () {

		if (vigencia_enc == 0) {

			$("#titulo_noti_promo").html("Crear Preguntas");

			limpiar_form();
			tipo_crud = 1;
			id_pre = 0;
			cant_resp = 0;
			ver_orden(0, 0);

			$('#modal_crud').modal('show'); // Abre el modal donde se crea el formulario.

		} else {

			BootstrapDialog.show({
				title: "Crear Preguntas",
				closable: true,
				closeByBackdrop: false,
				closeByKeyboard: false,
				size: BootstrapDialog.SIZE_WIDE,
				message: '<center>No es posible crear la pregunta mientras la encuesta esté vigente</center>',
				buttons: [{
					label: 'Cerrar',
					cssClass: 'btn',
					action: function (dialog) {
						dialog.close();
					}
				}]
			});

		}
	})

	//Funcion para cuando se haga el submit del form.
	$("#frm_crud").submit(function (event) {
		event.preventDefault();
		crud();
	});

	info_encuesta();
	ver_respuestas();

	$("#frm_b_noti").submit(function (event) {
		Cargar_Tabla()
		event.preventDefault();
	});

	$("#CSV").unbind("click");
	$("#CSV").click(function () {

		var titulob = $("#titulob").val();
		var tipob = $("#tipob").val();
		var estadob = $("#estadob").val();
		var userb = $("#userb").val();

		fetch(`${URL_MODULO}/controlador.php`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				accion: 'Descargar_Csv',
				titulob: titulob,
				tipob: tipob,
				estadob: estadob,
				userb: userb
			})
		}).then(response => response.json()).then(data => {
			if (!$.isEmptyObject(data)) {
				var header = "TIPO, TITULO, DESCRIPCION, URL, PALABRAS CLAVES, IMAGEN, ARCHIVO, ESTADO, VIGENCIA, FECHA CREA, USUARIO CREA, FECHA MODIFICA, USUARIO MODIFICA";
				var cols = "0,1,2,3,4,5,6,7,8,9,10,11,12";
				ExportarCSV(data, header, "Reporte Encuesta", cols);
			}
			else {
				Notificacion("No se encontraron datos para descargar en el archivo", "warning");
			}
		}).catch(error => {
			console.error('Error:', error);
			Notificacion("Error al descargar el archivo", "error");
		});
	})

});

function info_encuesta() {
	fetch(`${URL_MODULO}/controlador.php`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			accion: 'info_encuesta',
			id_enc: id_enc
		})
	}).then(response => response.json()).then(data => {
		$.each(data, function (index, row) {

			$("#t_encuesta").html(row.titulo);
			$("#o_encuesta").html(row.obliga);
			$("#d_encuesta").html(row.descripcion);
			$("#vigencia").html(row.est_vi);

			vigencia_enc = row.vigente;

		});
	}).catch(error => {
		console.error('Error:', error);
		Notificacion("Error al cargar información de la encuesta", "error");
	});
}

function ver_orden(order, op) {
	fetch(`${URL_MODULO}/controlador.php`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			accion: 'ver_orden',
			id_enc: id_enc
		})
	}).then(response => response.json()).then(data => {
		var orden = "<option value=''>Seleccionar...</option>";
		var selected = "";
		if (order == 0 && op > 0) {
			selected = "selected";
		}
		orden += "<option value='0' " + selected + ">Al principio</option>";

		$.each(data, function (index, row) {

			var selected = "";

			if (order > 0 && order == row.orden) {
				selected = "selected";
			}

			if (op > 0 && id_pre != row.id) {

				orden += "<option value=" + row.orden + " " + selected + ">Después: " + row.pregunta_enc + "</option>";

			} else if (op == 0) {

				orden += "<option value=" + row.orden + " " + selected + ">Después: " + row.pregunta_enc + "</option>";

			}

		});

		$("#orden").html(orden).change();
	}).catch(error => {
		console.error('Error:', error);
		Notificacion("Error al cargar el orden", "error");
	});
}

function ver_respuestas() {
	fetch(`${URL_MODULO}/controlador.php`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			accion: 'ver_respuestas',
			id_enc: id_enc
		})
	}).then(response => response.json()).then(data => {
		var html = "<option value=''>Seleccionar...</option>";
		$.each(data, function (index, row) {
			html += "<option value=" + row.id_pregunta + " >" + row.pregunta_enc + "</option>";
		});

		$("#respuestab").html(html).change();
	}).catch(error => {
		console.error('Error:', error);
		Notificacion("Error al cargar las respuestas", "error");
	});
}

function Cargar_Tabla() {

	var tipop = $("#tipop").val();
	var obliga = $("#obliga").val();
	var respuestab = $("#respuestab").val();
	var estadob = $("#estadob").val();

	var cont = 0;
	if (!$.fn.DataTable.isDataTable('#tbl')) {

		dtable = $("#tbl").DataTable({

			ajax: function (data, callback, settings) {
				fetch(`${URL_MODULO}/controlador.php`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						accion: 'Consultar',
						tipop: tipop,
						obliga: obliga,
						respuestab: respuestab,
						estadob: estadob,
						id_pre: id_pre,
						id_enc: id_enc
					})
				}).then(response => response.json()).then(result => {
					callback(result);
				}).catch(error => {
					console.error('Error:', error);
					callback({
						draw: 1,
						recordsTotal: 0,
						recordsFiltered: 0,
						data: []
					});
				});
			},
			"bFilter": false,
			"order": [],
			"responsive": true,
			"language": {
				"emptyTable": "Ningún dato",
				"infoEmpty": "No se ha encontrado ningún resultado"
			},
			"columns": [
				{ "data": "pregunta_enc" },
				{ "data": "tipo_respuesta" },
				{ "data": "obliga" },
				{ "data": "cant_resp" },
				{ "data": "estado" },
				{ "data": "estado" },
				{ "data": "estado" }
			],
			"columnDefs": [
				{
					"targets": 4,
					"data": "estado",
					render: function (data, type, row) {
						if (data == "1") {
							return '<i class="glyphicon glyphicon-ok-circle estado" title="Activo" style="cursor:pointer; font-size: 25px; color: #10628a; margin-top: 7px;" data-id_pre="' + row.id_pre + '"></i>';
						}
						if (data == "0") {
							return '<i class="glyphicon glyphicon-ban-circle estado" title="Inactivo" style="cursor:pointer;font-size: 25px; color: #DE0A0B; margin-top: 7px;" data-id_pre="' + row.id_pre + '"></i>';
						}
					}
				},
				{
					"targets": 5,
					"data": "",
					render: function (data, type, row, rowcol) {

						return '<i class="glyphicon glyphicon-plus-sign s_respuestas" title="Inactivo" style="font-size: 25px; color: #10628a; margin-top: 7px;cursor:pointer;" data-id_pre="' + row.id_pre + '" data-titulo="' + row.titulo_enc + '"></i>';

					}
				},
				{
					"targets": 6,
					"data": "",
					render: function (data, type, row, rowcol) {

						return '<button class="btn btn-sm btn-primary editar" data-id_pre="' + row.id_pre + '" data-obliga="' + row.obligatorio + '" data-tipo="' + row.tipo + '" data-orden="' + row.orden + '"><i class="fa fa-edit" title="Editar"></i></button>';
					}
				}
			],

			fnDrawCallback: function () {

				$(".editar").unbind("click");
				$(".editar").click(function () {

					var data = dtable.row($(this).parents('tr')).data();

					if (vigencia_enc == 0) {

						limpiar_form();
						ver_orden(parseInt($(this).attr("data-orden")) - 1, 1);



						id_pre = $(this).attr("data-id_pre");
						$("#pregunta").val(data["pregunta_enc"]);


						$("input:radio[name=c_obliga][value='" + $(this).attr("data-obliga") + "']").prop('checked', true);

						$("#tipopc").val($(this).attr("data-tipo")).attr("selected");

						$("#titulo_noti_promo").html("Editar Pregunta");
						$('#modal_crud').modal('show');
						tipo_crud = 2;

					} else {
						BootstrapDialog.show({
							title: data["pregunta_enc"],
							closable: true,
							closeByBackdrop: false,
							closeByKeyboard: false,
							size: BootstrapDialog.SIZE_WIDE,
							message: '<center>No es posible editar la pregunta mientras la encuesta esté vigente</center>',
							buttons: [{
								label: 'Cerrar',
								cssClass: 'btn',
								action: function (dialog) {
									dialog.close();
								}
							}]
						});
					}

				});

				$(".s_respuestas").unbind("click");
				$(".s_respuestas").click(function () {

					id_pre = $(this).attr("data-id_pre");

					localStorage.setItem('idPreguntaData', id_pre);
					window.location = "?mod=crear_respuestas";
				});

				$(".estado").unbind("click");
				$(".estado").click(function () {

					var data = dtable.row($(this).parents('tr')).data();

					if (vigencia_enc == 0) {

						id_pre = $(this).attr("data-id_pre");
						var estado = data['estado'];

						BootstrapDialog.confirm("¿Está seguro de cambiar el estado de la encuesta?", function (result) {
							if (result) {
								fetch(`${URL_MODULO}/controlador.php`, {
									method: 'POST',
									headers: {
										'Content-Type': 'application/json'
									},
									body: JSON.stringify({
										accion: 'cambiar_estado',
										id_enc: id_enc,
										estado: estado,
										id_pre: id_pre
									})
								}).then(response => response.json()).then(data => {
									if (data["estado"] == 1) {
										Notificacion(data["msg"], "success");
										Cargar_Tabla();
									} else if (data["estado"] == 0) {
										Notificacion(data["msg"], "error");
									}
								}).catch(error => {
									console.error('Error:', error);
									Notificacion("Error al cambiar el estado", "error");
								});
							}
						});

					} else {
						BootstrapDialog.show({
							title: data["pregunta_enc"],
							closable: true,
							closeByBackdrop: false,
							closeByKeyboard: false,
							size: BootstrapDialog.SIZE_WIDE,
							message: '<center>No es posible cambiar el estado de la pregunta mientras la encuesta esté vigente</center>',
							buttons: [{
								label: 'Cerrar',
								cssClass: 'btn',
								action: function (dialog) {
									dialog.close();
								}
							}]
						});
					}
				})

			}
		});

		$("#consulta").show();

	}
	else {
		dtable.destroy();
		Cargar_Tabla();
	}
}

function crud() {

	var pregunta = $("#pregunta").val();
	var orden = $("#orden").val();
	var tipopc = $("#tipopc").val();
	var check_obliga = $('input:radio[name=c_obliga]:checked').val();

	BootstrapDialog.confirm('¿Está seguro de enviar los datos?', function (result) {
		if (result) {

			fetch(`${URL_MODULO}/controlador.php`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					accion: 'crud',
					pregunta: pregunta,
					orden: orden,
					tipopc: tipopc,
					check_obliga: check_obliga,
					tipo_crud: tipo_crud,
					id_pre: id_pre,
					id_enc: id_enc
				})
			}).then(response => response.json()).then(data => {
				$.each(data, function (index, fila) {

					if (fila.estado == 1) {
						Notificacion(fila.msg, "success");

						Cargar_Tabla();
						$('.modal').modal('hide');

					} else {
						Notificacion(fila.msg, "error");
					}
				});
			}).catch(error => {
				console.error('Error:', error);
				Notificacion("Error al procesar los datos", "error");
			});
		}
	});

}

function limpiar_form() {

	$("#pregunta").val("");

	$('#orden').val("").change();
	$('#tipopc').val("").change();

	$('input:radio[name=c_obliga]:checked').prop('checked', false);
	cant_resp = 0;

}
