<?php
require_once "../../config/mainController.php";
require_once "./modelo.php";

$controller = new mainController;
$modelo = new crear_encuenta();

try {
  $metodo = $_SERVER['REQUEST_METHOD'];
  $tipo_res = "";
  $response = null;
  $variables = array();

  // OBLIGATORIO: Manejar JSON y POST tradicional
  if (!empty($_POST)) {
    $variables = $_POST;
  } else if ($metodo == 'POST' && $_SERVER['CONTENT_TYPE'] == 'application/json') {
    $json = file_get_contents('php://input');
    $variables = json_decode($json, true);
  }

  // OBLIGATORIO: Validar acción
  if (!isset($variables['accion'])) {
    http_response_code(404);
    header("Content-type: text/plain; charset=utf-8");
    echo "0";
    return;
  }

  $accion = $variables['accion'];

  // Dependiendo de la accion se ejecutaran las tareas y se definira el tipo de respuesta
  switch ($accion) {
    case 'cargar_campos':
      $tipo_res = 'JSON';
      $response = $modelo->cargar_campos();
      break;

    case "perfiles_operador":
      $tipo_res = "JSON";
      $response = $modelo->perfiles_operador();
      break;

    case 'crud_encuesta':
      $tipo_res = 'JSON';
      $titulo = $variables['titulo'];
      $descrip = $variables['descrip_e'];
      $fecha_ini = $variables['fecha_ini'];
      $fecha_fin = $variables['fecha_fin'];
      $check_obliga = $variables['check_obliga'];
      $check_navega = $variables['check_navega'];
      $tipo_crud = $variables['tipo_crud'];
      $id_encuesta = $variables['id_encuesta'];
      $aplicar_encuesta = $variables['aplicar_encuesta'];
      $reporte_distri = $variables["reporte_distri"];

      if ($tipo_crud < 3) {
        $response = $modelo->recibirDatos($titulo, $descrip, $fecha_ini, $fecha_fin, $check_obliga, $check_navega, $tipo_crud, $id_encuesta, $aplicar_encuesta, $reporte_distri);
      } else {
        $response = $modelo->duplicarEncuesta($titulo, $descrip, $fecha_ini, $fecha_fin, $check_obliga, $check_navega, $tipo_crud, $id_encuesta, $aplicar_encuesta, $reporte_distri, 1);
      }

      break;

    case 'ver_titulos':
      $tipo_res = 'JSON';
      $op = $variables['op'];
      $response = $modelo->ver_titulos($op);
      break;

    case 'ver_preguntas':
      $tipo_res = 'JSON';
      $idEncuesta = $variables['idEncuesta'];
      $idRespuesta = $variables['idRespuesta'];
      $response = $modelo->ver_preguntas($idEncuesta, $idRespuesta);
      break;

    case 'ver_respuestas':
      $tipo_res = 'JSON';
      $idEncuesta = $variables['idEncuesta'];
      $idPregunta = $variables['idPregunta'];
      $response = $modelo->ver_respuestas($idEncuesta, $idPregunta);
      break;

    case 'Consultar':
      $tipo_res = 'JSON';
      $fecha_ini = $variables['fecha_ini_b'];
      $fecha_fin = $variables['fecha_fin_b'];
      $titulob = $variables['titulob'];
      $estadob = $variables['estadob'];
      $datos = $modelo->Consultar($fecha_ini, $fecha_fin, $titulob, $estadob);
      $response = array(
        "draw" => 1,
        "recordsTotal" => 0,
        "recordsFiltered" => 0,
        "data" => $datos
      );

      break;

    case 'deta_preguntas':
      $tipo_res = 'JSON';
      $id_enc = $variables['id_enc'];
      $response = $modelo->Consultar_deta($id_enc);
      break;

    case 'cambiar_estado':
      $tipo_res = 'JSON';
      $estado = $variables['estado'];
      $id_enc = $variables['id_enc'];
      $response = $modelo->cambiar_estado($estado, $id_enc);
      break;

    case 'reporte_distri':
      $tipo_res = 'JSON';
      $reporte_distri = $variables['reporte_distri'];
      $id_enc = $variables['id_enc'];
      $response = $modelo->reporte_distri($reporte_distri, $id_enc);
      break;

    case 'eliminar_encuesta':
      $tipo_res = 'JSON';
      $id_enc = $variables['id_enc'];
      $response = $modelo->eliminar_encuesta($id_enc);
      break;

    case 'cargar_dcs':
      $tipo_res = 'JSON';
      $id_enc = $variables['id_enc'];
      $response = $modelo->cargar_dcs($id_enc);
      break;

    case 'validar_encuesta':
      $tipo_res = 'HTML';
      $id_enc = $variables['id_enc'];
      $response = $modelo->validar_encuesta($id_enc);
      break;

    case 'guardar_asignacion':
      $tipo_res = 'JSON';
      $id_enc = $variables['id_enc'];

      // Paso 1: operador
      $operador_app = $variables['operador_app'];

      // Paso 1: distribuidor
      $pdv = $variables['pdv'];
      $vendedor_pdv = $variables['vendedor_pdv'];
      $supervisor_pdv = $variables['supervisor_pdv'];

      // Paso 2: operador
      $operador_sistema = $variables['operador_sistema'];

      // Paso 2: distribuidor
      $app = $variables['app'];
      $vendedor = $variables['vendedor'];
      $supervisor = $variables['supervisor'];
      $usuario_sistema = $variables['usuario_sistema'];

      // Paso 2: PDV (punto recarga)
      $punto_recargar = $variables['punto_recargar'];

      /**
       * Paso 3: asignación DCS
       */
      $in_regional = array();
      $in_dis = array();
      $in_ter = array();
      $in_zonas = array();

      if (isset($variables['in_regional']))
        $in_regional = $modelo->decompress($modelo->Hex2String($variables["in_regional"]));

      if (isset($variables['in_dis']))
        $in_dis = $modelo->decompress($modelo->Hex2String($variables["in_dis"]));

      if (isset($variables['in_ter']))
        $in_ter = $modelo->decompress($modelo->Hex2String($variables["in_ter"]));

      if (isset($variables['in_zonas']))
        $in_zonas = $modelo->decompress($modelo->Hex2String($variables["in_zonas"]));

      $response = $modelo->guardar_asignacion($id_enc, $app, $vendedor, $supervisor, $pdv, $vendedor_pdv, $supervisor_pdv, $in_regional, $in_dis, $in_ter, $in_zonas, $punto_recargar, $usuario_sistema, $operador_app, $operador_sistema);
      break;

    case 'ordenar':
      $tipo_res = 'JSON';
      $op = $variables['op'];
      $id_encuesta = $variables['id_encuesta'];
      $id_respuesta = $variables['id_respuesta'];
      $id_pregunta = $variables['id_pregunta'];
      $posicion = $variables['posicion'];
      $response = $modelo->ordenar($op, $id_encuesta, $id_respuesta, $id_pregunta, $posicion);
      break;

    case 'buscar_ramificacion':
      $tipo_res = 'JSON';
      $id_encuesta = $variables['id_encuesta'];
      $response = $modelo->buscar_ramificacion($id_encuesta);
      break;

    default:
      http_response_code(404);
      header("Content-type: text/plain; charset=utf-8");
      echo "0";
      return;
  }

  // OBLIGATORIO: Headers y manejo de respuestas
  if ($tipo_res == "JSON") {
    header("Content-type: application/json; charset=utf-8");
    $json_output = json_encode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new Exception("Error al codificar JSON: " . json_last_error_msg());
    } else {
      echo $json_output;
    }
  } elseif ($tipo_res == "HTML") {
    header("Content-type: text/html; charset=utf-8");
    echo $response;
  } else {
    header("Content-type: text/plain; charset=utf-8");
    echo $response;
  }
} catch (Exception $e) {
  header("Content-type: application/json; charset=utf-8");
  http_response_code(500);
  echo json_encode(array("error" => true, "mensaje" => $e->getMessage()));
} catch (Error $e) {
  header("Content-type: application/json; charset=utf-8");
  http_response_code(500);
  echo json_encode(array("error" => true, "mensaje" => $e->getMessage()));
}
